import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-welcome-to-receeto',
  standalone: true,
  imports: [],
  templateUrl: './welcome-to-receeto.component.html',
  styleUrls: ['./welcome-to-receeto.component.css']
})
export class WelcomeToReceetoComponent {
  constructor(private router: Router) {}

  selectRole(role: 'shopper' | 'seller') {
    this.router.navigate(['/login'], { queryParams: { role: role } });
  }
}