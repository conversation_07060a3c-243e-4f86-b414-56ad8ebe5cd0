import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { UserService } from '../services/user.service';
import { User } from '../models/user.model';
import { NotificationPanelComponent } from '../notification-system/notification-panel.component';

@Component({
  selector: 'app-user-profile',
  standalone: true,
  // NotificationPanelComponent is imported here because it detects when it's on the user profile page
  // and positions itself accordingly via the top-navbar component
  imports: [CommonModule, FormsModule, TopNavbarComponent, SidebarComponent, NotificationPanelComponent],
  templateUrl: './user-profile.component.html',
  styleUrl: './user-profile.component.scss'
})
export class UserProfileComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('previewImage') previewImage!: ElementRef;

  user!: User;
  editingField: string | null = null;
  tempFieldValue: any = null;
  editingAllFields = false;
  tempUser: User | null = null;

  // Image repositioning properties
  showImageRepositionModal = false;
  tempImageSrc: string | null = null;
  tempImageFile: File | null = null;
  tempImagePosition = 0;
  isDragging = false;
  dragStartY = 0;
  dragStartPosition = 0;
  animationFrameId: number | null = null;

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.userService.currentUser$.subscribe(user => {
      this.user = user;
    });
  }

  startEditing(field: keyof User): void {
    this.editingField = field;

    if (field === 'role') {
      // Initialize occupation type and place
      this.occupationType = this.user.occupationType || null;
      this.occupationPlace = this.user.occupationPlace || null;
      this.tempFieldValue = this.user[field];
    } else if (field === 'phoneNumber') {
      // Initialize phone number without prefix
      if (this.user[field] && this.user[field].startsWith(this.phonePrefix)) {
        // If the phone number already has the prefix, remove it for editing
        this.phoneNumberValue = this.user[field].substring(this.phonePrefix.length);
        this.tempFieldValue = this.phoneNumberValue;
      } else if (this.user[field]) {
        // If there's a phone number but no prefix
        this.phoneNumberValue = this.user[field];
        this.tempFieldValue = this.phoneNumberValue;
      } else {
        // Default placeholder
        this.phoneNumberValue = '00 000 000';
        this.tempFieldValue = '';
      }
    } else {
      this.tempFieldValue = this.user[field];
    }
  }

  // Phone number focus/blur handlers
  onPhoneFocus(): void {
    this.isPhoneFocused = true;
    if (this.phoneNumberValue === '00 000 000') {
      this.phoneNumberValue = '';
      this.tempFieldValue = '';
    }
  }

  onPhoneBlur(): void {
    this.isPhoneFocused = false;
    if (!this.phoneNumberValue) {
      this.phoneNumberValue = '00 000 000';
    }
  }

  // Handle phone number input
  onPhoneInput(event: any): void {
    // Filter out any non-numeric characters except spaces
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    const originalValue = this.phoneNumberValue;

    // Check if the input contains any non-numeric characters (except spaces)
    const hasNonNumeric = /[^\d\s]/.test(originalValue);

    // Replace any non-numeric and non-space characters
    const filteredValue = originalValue.replace(/[^0-9 ]/g, '');

    // Only update if the value has changed (something was filtered out)
    if (filteredValue !== originalValue) {
      this.phoneNumberValue = filteredValue;

      // Show error message if letters were entered
      if (hasNonNumeric) {
        this.phoneNumberError = "Only numbers are allowed";

        // Hide error after 3 seconds
        setTimeout(() => {
          if (this.phoneNumberError === "Only numbers are allowed") {
            this.phoneNumberError = null;
          }
        }, 3000);
      }

      // Restore cursor position after Angular updates the DOM
      setTimeout(() => {
        // Adjust cursor position if characters were removed before the cursor
        const newPosition = Math.max(0, cursorPosition - (originalValue.length - filteredValue.length));
        input.setSelectionRange(newPosition, newPosition);
      });
    }

    // Validate phone number length (excluding spaces)
    const digitsOnly = filteredValue.replace(/\s/g, '');
    if (digitsOnly.length > 0 && digitsOnly.length < 8) {
      this.phoneNumberError = "Invalid number (must be at least 8 digits)";
    } else if (digitsOnly.length >= 8) {
      this.phoneNumberError = null;
    }

    // Update the full phone number in the tempFieldValue
    this.tempFieldValue = this.phonePrefix + this.phoneNumberValue;
  }

  cancelEditing(): void {
    this.editingField = null;
    this.tempFieldValue = null;
    this.occupationType = null;
    this.occupationPlace = null;
    this.phoneNumberError = null;
  }

  // Helper function to capitalize first letter of each word
  capitalizeFirstLetter(text: string): string {
    if (!text) return text;
    return text.split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // For occupation type and place
  occupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | null = null;
  occupationPlace: string | null = null;

  // For phone number with country code
  phonePrefix = '+216 ';
  phoneNumberValue = '00 000 000';
  isPhoneFocused = false;
  phoneNumberError: string | null = null;

  saveField(field: keyof User): void {
    if (this.tempFieldValue !== null) {
      // Handle special cases for placeholders
      if (field === 'fullName' && this.tempFieldValue) {
        // Capitalize first letter of each word in full name
        const capitalizedName = this.capitalizeFirstLetter(this.tempFieldValue);
        const updatedUser = { ...this.user, fullName: capitalizedName, showFullNamePlaceholder: false };
        this.userService.updateUser(updatedUser).subscribe();
      } else if (field === 'gender' && this.tempFieldValue) {
        const updatedUser = { ...this.user, gender: this.tempFieldValue, showGenderPlaceholder: false };
        this.userService.updateUser(updatedUser).subscribe();
      } else if (field === 'address' && this.tempFieldValue) {
        const updatedUser = { ...this.user, address: this.tempFieldValue, showAddressPlaceholder: false };
        this.userService.updateUser(updatedUser).subscribe();
      } else if (field === 'phoneNumber') {
        // For phone number, ensure it has the country code prefix
        let phoneValue = this.tempFieldValue;

        // If it's the default placeholder or empty, don't save
        if (phoneValue === this.phonePrefix + '00 000 000' || phoneValue === this.phonePrefix) {
          this.editingField = null;
          this.tempFieldValue = null;
          this.phoneNumberError = null;
          return;
        }

        // Validate phone number length
        const digitsOnly = phoneValue.replace(/[^\d]/g, '');
        if (digitsOnly.length < 8) {
          this.phoneNumberError = "Invalid number (must be at least 8 digits)";
          return; // Don't save if invalid
        }

        // If it doesn't start with the prefix, add it
        if (!phoneValue.startsWith(this.phonePrefix)) {
          phoneValue = this.phonePrefix + phoneValue;
        }

        const updatedUser = { ...this.user, phoneNumber: phoneValue };
        this.userService.updateUser(updatedUser).subscribe();
        this.phoneNumberError = null;
      } else if (field === 'role') {
        // For occupation, we now have type and place
        if (this.occupationType) {
          let updatedUser: User;

          if (this.occupationType === 'Unemployed') {
            // If unemployed, we don't need place
            updatedUser = {
              ...this.user,
              role: this.occupationType,
              occupationType: this.occupationType,
              occupationPlace: '',
              showOccupationPlaceholder: false
            };
          } else {
            // For other types, we need both type and place
            updatedUser = {
              ...this.user,
              role: this.occupationType + (this.occupationPlace ? ` at ${this.occupationPlace}` : ''),
              occupationType: this.occupationType,
              occupationPlace: this.occupationPlace || '',
              showOccupationPlaceholder: false
            };
          }

          this.userService.updateUser(updatedUser).subscribe();
        }
      } else {
        this.userService.updateUserField(field, this.tempFieldValue).subscribe();
      }

      this.editingField = null;
      this.tempFieldValue = null;
      this.occupationType = null;
      this.occupationPlace = null;
      this.isPhoneFocused = false;
    }
  }

  startEditingAll(): void {
    this.editingAllFields = true;
    this.tempUser = { ...this.user };

    // Initialize occupation type and place for edit all form
    this.tempOccupationType = this.user.occupationType || null;
    this.tempOccupationPlace = this.user.occupationPlace || null;

    // Initialize phone number value
    if (this.user.phoneNumber && this.user.phoneNumber.startsWith(this.phonePrefix)) {
      this.tempPhoneNumberValue = this.user.phoneNumber.substring(this.phonePrefix.length);
    } else if (this.user.phoneNumber) {
      this.tempPhoneNumberValue = this.user.phoneNumber;
    } else {
      this.tempPhoneNumberValue = '00 000 000';
    }
  }

  cancelEditingAll(): void {
    this.editingAllFields = false;
    this.tempUser = null;
    this.tempOccupationType = null;
    this.tempOccupationPlace = null;
    this.tempPhoneNumberValue = '';
    this.tempPhoneNumberError = null;
  }

  // For edit all form
  tempOccupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | null = null;
  tempOccupationPlace: string | null = null;
  tempPhoneNumberValue: string = '';
  tempPhoneNumberError: string | null = null;

  // Phone number handlers for edit all form
  onPhoneInputAll(event: any): void {
    // Filter out any non-numeric characters except spaces
    const input = event.target as HTMLInputElement;
    const cursorPosition = input.selectionStart || 0;
    const originalValue = this.tempPhoneNumberValue;

    // Check if the input contains any non-numeric characters (except spaces)
    const hasNonNumeric = /[^\d\s]/.test(originalValue);

    // Replace any non-numeric and non-space characters
    const filteredValue = originalValue.replace(/[^0-9 ]/g, '');

    // Only update if the value has changed (something was filtered out)
    if (filteredValue !== originalValue) {
      this.tempPhoneNumberValue = filteredValue;

      // Show error message if letters were entered
      if (hasNonNumeric) {
        this.tempPhoneNumberError = "Only numbers are allowed";

        // Hide error after 3 seconds
        setTimeout(() => {
          if (this.tempPhoneNumberError === "Only numbers are allowed") {
            this.tempPhoneNumberError = null;
          }
        }, 3000);
      }

      // Restore cursor position after Angular updates the DOM
      setTimeout(() => {
        // Adjust cursor position if characters were removed before the cursor
        const newPosition = Math.max(0, cursorPosition - (originalValue.length - filteredValue.length));
        input.setSelectionRange(newPosition, newPosition);
      });
    }

    // Validate phone number length (excluding spaces)
    const digitsOnly = filteredValue.replace(/\s/g, '');
    if (digitsOnly.length > 0 && digitsOnly.length < 8) {
      this.tempPhoneNumberError = "Invalid number (must be at least 8 digits)";
    } else if (digitsOnly.length >= 8) {
      this.tempPhoneNumberError = null;
    }

    // Update the full phone number in the user object
    if (this.tempUser) {
      this.tempUser.phoneNumber = this.phonePrefix + this.tempPhoneNumberValue;
    }
  }

  onPhoneFocusAll(): void {
    // If the value is the placeholder, clear it
    if (this.tempPhoneNumberValue === '00 000 000') {
      this.tempPhoneNumberValue = '';
    }
  }

  onPhoneBlurAll(): void {
    // If empty, restore the placeholder
    if (!this.tempPhoneNumberValue || this.tempPhoneNumberValue === '') {
      this.tempPhoneNumberValue = '';
      if (this.tempUser) {
        this.tempUser.phoneNumber = '';
      }
    } else {
      // Ensure the phone number has the prefix
      if (this.tempUser) {
        this.tempUser.phoneNumber = this.phonePrefix + this.tempPhoneNumberValue;
      }
    }
  }

  saveAllFields(): void {
    if (this.tempUser) {
      // Validate phone number length
      if (this.tempPhoneNumberValue && this.tempPhoneNumberValue !== '00 000 000') {
        const digitsOnly = this.tempPhoneNumberValue.replace(/[^\d]/g, '');
        if (digitsOnly.length < 8) {
          this.tempPhoneNumberError = "Invalid number (must be at least 8 digits)";
          return; // Don't save if phone number is invalid
        }
      }

      // Update placeholder flags based on filled fields
      if (this.tempUser.fullName) {
        // Capitalize first letter of each word in full name
        this.tempUser.fullName = this.capitalizeFirstLetter(this.tempUser.fullName);
        this.tempUser.showFullNamePlaceholder = false;
      }
      if (this.tempUser.gender) {
        this.tempUser.showGenderPlaceholder = false;
      }
      if (this.tempUser.address) {
        this.tempUser.showAddressPlaceholder = false;
      }

      // Handle occupation type and place
      if (this.tempOccupationType) {
        this.tempUser.occupationType = this.tempOccupationType;

        if (this.tempOccupationType === 'Unemployed') {
          this.tempUser.occupationPlace = '';
          this.tempUser.role = this.tempOccupationType;
        } else {
          this.tempUser.occupationPlace = this.tempOccupationPlace || '';
          this.tempUser.role = this.tempOccupationType +
            (this.tempOccupationPlace ? ` at ${this.tempOccupationPlace}` : '');
        }

        this.tempUser.showOccupationPlaceholder = false;
      }

      this.userService.updateUser(this.tempUser).subscribe();
      this.editingAllFields = false;
      this.tempUser = null;
      this.tempOccupationType = null;
      this.tempOccupationPlace = null;
      this.tempPhoneNumberValue = '';
      this.tempPhoneNumberError = null;
    }
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  openImagePositionModal(): void {
    if (this.user.hasCustomAvatar && this.user.avatar) {
      // Create a temporary image source from the current avatar
      this.tempImageSrc = this.user.avatar;
      this.tempImagePosition = this.user.avatarPositionY || 0;
      this.showImageRepositionModal = true;

      // Wait for the modal to be rendered, then initialize the image position
      setTimeout(() => {
        if (this.previewImage && this.previewImage.nativeElement) {
          this.previewImage.nativeElement.style.transform = `translate3d(0, ${this.tempImagePosition}px, 0)`;
        }
      }, 50);
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.tempImageFile = file;
      const reader = new FileReader();

      reader.onload = (e) => {
        this.tempImageSrc = e.target?.result as string;
        this.tempImagePosition = 0; // Reset position
        this.showImageRepositionModal = true;

        // Wait for the modal to be rendered, then initialize the image position
        setTimeout(() => {
          if (this.previewImage && this.previewImage.nativeElement) {
            this.previewImage.nativeElement.style.transform = 'translate3d(0, 0, 0)';
          }
        }, 50);
      };

      reader.readAsDataURL(file);
    }
  }

  // Image repositioning methods with extreme performance optimization
  startDragging(event: MouseEvent): void {
    // Store initial values
    this.isDragging = true;
    this.dragStartY = event.clientY;
    this.dragStartPosition = this.tempImagePosition;

    // Prevent text selection and default behaviors
    event.preventDefault();

    // Add global event listeners for smoother tracking
    window.addEventListener('mousemove', this.handleMouseMove);
    window.addEventListener('mouseup', this.handleMouseUp);
  }

  // Use arrow function to preserve 'this' context
  private handleMouseMove = (event: MouseEvent): void => {
    if (!this.isDragging || !this.previewImage) return;

    // Calculate the new position directly without state updates
    const deltaY = event.clientY - this.dragStartY;
    const newPosition = this.dragStartPosition + deltaY;

    // Limit the movement range
    const maxOffset = 200;
    const limitedPosition = Math.max(-maxOffset, Math.min(maxOffset, newPosition));

    // Only update if position changed to avoid unnecessary renders
    if (this.tempImagePosition !== limitedPosition) {
      // Use direct DOM manipulation with ViewChild for maximum performance
      const imageElement = this.previewImage.nativeElement;

      // Apply transform directly to DOM for instant feedback
      // Using style.transform is faster than setAttribute
      imageElement.style.transform = `translate3d(0, ${limitedPosition}px, 0)`;

      // Update state only once per animation frame for Angular binding
      if (!this.animationFrameId) {
        this.animationFrameId = requestAnimationFrame(() => {
          this.tempImagePosition = limitedPosition;
          this.animationFrameId = null;
        });
      }
    }
  }

  // Use arrow function to preserve 'this' context
  private handleMouseUp = (): void => {
    this.stopDragging();
  }

  stopDragging(): void {
    if (!this.isDragging) return;

    this.isDragging = false;

    // Remove global event listeners
    window.removeEventListener('mousemove', this.handleMouseMove);
    window.removeEventListener('mouseup', this.handleMouseUp);

    // Cancel any pending animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  saveRepositionedImage(): void {
    if (this.tempImageSrc) {
      // Make sure we have the latest position value
      if (this.previewImage && this.previewImage.nativeElement) {
        // Extract the current transform value directly from the DOM element
        const transformStyle = this.previewImage.nativeElement.style.transform;
        const match = transformStyle.match(/translate3d\(0px,\s*(-?\d+(?:\.\d+)?)px,\s*0px\)/);

        if (match && match[1]) {
          this.tempImagePosition = parseFloat(match[1]);
        }
      }

      const updatedUser = {
        ...this.user,
        avatar: this.tempImageSrc,
        avatarPositionY: this.tempImagePosition,
        hasCustomAvatar: true
      };

      this.userService.updateUser(updatedUser).subscribe({
        next: (user) => {
          console.log('Avatar position updated successfully:', user.avatarPositionY);
          this.closeImageRepositionModal();
        },
        error: (err) => {
          console.error('Error updating avatar position:', err);
          this.closeImageRepositionModal();
        }
      });
    }
  }

  cancelImageRepositioning(): void {
    this.closeImageRepositionModal();
  }

  closeImageRepositionModal(): void {
    this.showImageRepositionModal = false;
    this.tempImageSrc = null;
    this.tempImageFile = null;
    this.tempImagePosition = 0;
    this.isDragging = false;

    // Cancel any pending animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  removeAvatar(): void {
    const updatedUser = {
      ...this.user,
      avatar: 'assets/images/default-avatar.svg',
      hasCustomAvatar: false
    };
    this.userService.updateUser(updatedUser).subscribe();
  }

  ngOnDestroy(): void {
    // Clean up all resources
    this.stopDragging();

    // Cancel any pending animation frames
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }
}
