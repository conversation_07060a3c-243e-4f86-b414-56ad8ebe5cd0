<div class="container">
  <div class="left-section">
    <h1>Create account</h1>
    <p>Already have an account? <a routerLink="/login">Login</a></p>
    <form (ngSubmit)="onSubmit()" #createAccountForm="ngForm">
      <div class="name-fields">
        <input type="text" placeholder="First Name" name="firstName" [(ngModel)]="accountData.firstName" required>
        <input type="text" placeholder="Last Name" name="lastName" [(ngModel)]="accountData.lastName" required>
      </div>
      <input type="email" placeholder="Email" name="email" [(ngModel)]="accountData.email" required email>
      <div class="password-field">
        <input [type]="showPassword ? 'text' : 'password'" placeholder="Password" name="password" [(ngModel)]="accountData.password" required minlength="8">
        <span class="toggle-password" (click)="togglePassword()">👁️</span>
      </div>
      <div class="terms">
        <input type="checkbox" name="terms" [(ngModel)]="accountData.terms" required>
        <label>I agree to DOPEsaaS's <a href="#">Terms of service</a> and <a href="#">Privacy policy</a></label>
      </div>
      <button type="submit" class="create-account-btn" [disabled]="!createAccountForm.valid">CREATE ACCOUNT</button>
    </form>
    <div class="divider">or</div>
    <button class="third-party google">
      <i class="fab fa-google"></i> Continue with Google
    </button>
    <button class="third-party apple">
      <i class="fab fa-apple"></i> Continue with Apple
    </button>
    <p class="success-message" *ngIf="successMessage">{{ successMessage }}</p>
    <p class="error-message" *ngIf="errorMessage">{{ errorMessage }}</p>
    <footer>© 2023 Receeto ALL RIGHTS RESERVED.</footer>
  </div>
  <div class="right-section">
    <div class="receeto-logo"></div>
    <div class="bottom-nav">
      <a href="https://receeto.com" target="_blank" class="nav-link">Website</a>
      <a href="#" class="nav-link">Documentation</a>
      <a href="#" class="nav-link">Terms of Use</a>
      <a href="#" class="nav-link">Blog</a>
    </div>
  </div>
</div>