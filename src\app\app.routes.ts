import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CreateAccountComponent } from './create-account/create-account.component';
import { LoginComponent } from './login/login.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { RegisterPersonnelInfoComponent } from './register-personnel-info/register-personnel-info.component';
import { WelcomeToReceetoComponent } from './welcome-to-receeto/welcome-to-receeto.component';
import { LayoutComponent } from './layout/layout.component';
import { ShopperDashboardComponent } from './shopper-dashboard/shopper-dashboard.component';
import { ShopperAnalyticsComponent } from './shopper-analytics/shopper-analytics.component';
import { ShopperOfferComponent } from './shopper-offer/shopper-offer.component';
import { ShopperReceetComponent } from './shopper-receet/shopper-receet.component';
import { ShopperTicketsHistoryComponent } from './shopper-tickets-history/shopper-tickets-history.component';
import { UserProfileComponent } from './user-profile/user-profile.component';
import { BudgetComponent } from './budget/budget.component';
import { NewBudgetComponent } from './new-budget/new-budget.component';
import { EditBudgetComponent } from './edit-budget/edit-budget.component';
import { FinancialSavingsComponent } from './financial-savings/financial-savings.component';
import { EditFinancialSavingsComponent } from './edit-financial-savings/edit-financial-savings.component';
import { SavingsCongratulationsComponent } from './savings-congratulations/savings-congratulations.component';
import { LoyaltyCardsComponent } from './loyalty-cards/loyalty-cards.component';
import { NewLoyaltyCardComponent } from './new-loyalty-card/new-loyalty-card.component';

export const routes: Routes = [
  { path: '', redirectTo: '/welcome-to-receeto', pathMatch: 'full' },
  { path: 'welcome-to-receeto', component: WelcomeToReceetoComponent },
  { path: 'login', component: LoginComponent },
  { path: 'create-account', component: CreateAccountComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'reset-password', component: ResetPasswordComponent },
  { path: 'register-personnel-info', component: RegisterPersonnelInfoComponent },

  // Shopper routes
  { path: 'shopper-dashboard', component: ShopperDashboardComponent },
  { path: 'shopper-analytics', component: ShopperAnalyticsComponent },
  { path: 'shopper-offer', component: ShopperOfferComponent },
  { path: 'shopper-receet', component: ShopperReceetComponent },
  { path: 'tickets/:id', component: ShopperTicketsHistoryComponent },
  { path: 'user-profile', component: UserProfileComponent },
  { path: 'budget', component: BudgetComponent },
  { path: 'new-budget', component: NewBudgetComponent },
  { path: 'edit-budget/:id', component: EditBudgetComponent },

  // Financial Savings routes
  { path: 'financial-savings', component: FinancialSavingsComponent },
  { path: 'edit-financial-savings', component: EditFinancialSavingsComponent },
  { path: 'edit-financial-savings/:id', component: EditFinancialSavingsComponent },
  { path: 'savings-congratulations', component: SavingsCongratulationsComponent },

  // Loyalty Cards routes
  { path: 'loyalty-cards', component: LoyaltyCardsComponent },
  { path: 'new-loyalty-card', component: NewLoyaltyCardComponent },

  // Seller routes
  {
    path: '',
    component: LayoutComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      {
        path: 'dashboard',
        loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule)
      },
      {
        path: 'analytics',
        loadChildren: () => import('./analytics/analytics.module').then(m => m.AnalyticsModule)
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }