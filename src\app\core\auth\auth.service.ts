import { Injectable, signal, computed } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthUser } from '../../interfaces/auth-user';
import { Login } from '../../interfaces/login';
import { CreationAccount } from '../../interfaces/creation-account';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // Auth state signals
  private _isAuthenticated = signal<boolean>(false);
  private _currentUser = signal<AuthUser | null>(null);
  private _isLoading = signal<boolean>(false);

  // Public readonly signals
  readonly isAuthenticated = this._isAuthenticated.asReadonly();
  readonly currentUser = this._currentUser.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();

  // Computed signals
  readonly userFullName = computed(() => this._currentUser()?.full_name || '');
  readonly userEmail = computed(() => this._currentUser()?.email || '');
  readonly userAvatar = computed(() => this._currentUser()?.avatar || 'assets/images/default-avatar.svg');

  constructor() {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    // In development mode, clear localStorage on initialization
    this.clearLocalStorageForDevelopment();

    // Try to load user from localStorage
    this.loadUserFromStorage();
  }

  private clearLocalStorageForDevelopment(): void {
    // Clear any existing auth data
    localStorage.removeItem('authUser');
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('registeredUser');
    console.log('Development mode: Auth localStorage cleared on initialization');
  }

  private loadUserFromStorage(): void {
    try {
      const storedUser = localStorage.getItem('authUser');
      const isAuth = localStorage.getItem('isAuthenticated') === 'true';

      if (storedUser && isAuth) {
        const user: AuthUser = JSON.parse(storedUser);
        this._currentUser.set(user);
        this._isAuthenticated.set(true);
        console.log('User loaded from localStorage:', user);
      }
    } catch (error) {
      console.error('Error loading user from localStorage:', error);
      this.logout();
    }
  }

  private saveUserToStorage(user: AuthUser): void {
    try {
      localStorage.setItem('authUser', JSON.stringify(user));
      localStorage.setItem('isAuthenticated', 'true');
    } catch (error) {
      console.error('Error saving user to localStorage:', error);
    }
  }

  private saveRegisteredUserToStorage(user: AuthUser, password: string): void {
    try {
      // Save user data with password for validation
      const userData = { ...user, password: password };
      localStorage.setItem('registeredUser', JSON.stringify(userData));
      console.log('User registration data saved to localStorage');
    } catch (error) {
      console.error('Error saving registered user to localStorage:', error);
    }
  }

  private getRegisteredUserFromStorage(): AuthUser | null {
    try {
      const storedUser = localStorage.getItem('registeredUser');
      return storedUser ? JSON.parse(storedUser) : null;
    } catch (error) {
      console.error('Error loading registered user from localStorage:', error);
      return null;
    }
  }

  register(accountData: CreationAccount): Observable<AuthUser> {
    this._isLoading.set(true);

    // Simulate API call
    return new Observable<AuthUser>(observer => {
      setTimeout(() => {
        try {
          // Check if user already exists
          const existingUser = this.getRegisteredUserFromStorage();
          if (existingUser && existingUser.email === accountData.email) {
            this._isLoading.set(false);
            observer.error(new Error('Email already registered. Please use a different email or login with existing account.'));
            return;
          }

          // Validate password strength
          if (accountData.password.length < 8) {
            this._isLoading.set(false);
            observer.error(new Error('Password is too weak. Please use at least 8 characters.'));
            return;
          }

          const newUser: AuthUser = {
            full_name: `${accountData.firstName} ${accountData.lastName}`,
            email: accountData.email,
            avatar: 'assets/images/default-avatar.svg',
            is_initialized: true
          };

          // Save user data with password but DON'T authenticate automatically
          this.saveRegisteredUserToStorage(newUser, accountData.password);
          this._isLoading.set(false);

          console.log('User registered successfully (not logged in):', newUser);
          observer.next(newUser);
          observer.complete();
        } catch (error) {
          this._isLoading.set(false);
          observer.error(new Error('Registration failed. Please try again.'));
        }
      }, 1000);
    });
  }

  login(loginData: Login): Observable<AuthUser> {
    this._isLoading.set(true);

    // Check if user exists in registered users and validate password
    return new Observable<AuthUser>(observer => {
      setTimeout(() => {
        try {
          const registeredUserData = localStorage.getItem('registeredUser');

          if (registeredUserData) {
            const userData = JSON.parse(registeredUserData);

            // Validate both email and password
            if (userData.email === loginData.email && userData.password === loginData.password) {
              // Remove password from user object before setting
              const { password, ...userWithoutPassword } = userData;
              const authenticatedUser: AuthUser = userWithoutPassword;

              this._currentUser.set(authenticatedUser);
              this._isAuthenticated.set(true);
              this.saveUserToStorage(authenticatedUser);
              this._isLoading.set(false);

              console.log('User logged in successfully:', authenticatedUser);
              observer.next(authenticatedUser);
              observer.complete();
            } else {
              this._isLoading.set(false);
              observer.error(new Error('Invalid email or password. Please check your credentials.'));
            }
          } else {
            this._isLoading.set(false);
            observer.error(new Error('No account found. Please create an account first.'));
          }
        } catch (error) {
          this._isLoading.set(false);
          observer.error(new Error('Login failed. Please try again.'));
        }
      }, 1000);
    });
  }

  logout(): void {
    this._currentUser.set(null);
    this._isAuthenticated.set(false);
    localStorage.removeItem('authUser');
    localStorage.removeItem('isAuthenticated');
    // Note: We keep 'registeredUser' so they can login again
    console.log('User logged out');
  }

  updateUser(userData: Partial<AuthUser>): Observable<AuthUser> {
    const currentUser = this._currentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      this._currentUser.set(updatedUser);
      this.saveUserToStorage(updatedUser);
      console.log('User updated:', updatedUser);
      return of(updatedUser);
    }
    return of(null as any);
  }
}
