export interface User {
  fullName: string;
  birthday: string;
  email: string;
  phoneNumber: string;
  governorate: string;
  gender: string;
  address: string;
  lastShoppingDate?: string;
  role?: string;
  avatar?: string;
  occupation?: string;
  occupationType?: 'Working' | 'Study' | 'Train' | 'Unemployed';
  occupationPlace?: string;
  showGenderPlaceholder?: boolean;
  showAddressPlaceholder?: boolean;
  showOccupationPlaceholder?: boolean;
  showFullNamePlaceholder?: boolean;
  hasCustomAvatar?: boolean;
  // Image positioning properties
  avatarPositionY?: number; // Vertical position offset for the avatar image
}
