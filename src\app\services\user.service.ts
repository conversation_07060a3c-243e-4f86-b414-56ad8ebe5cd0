import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private userRoleSubject = new BehaviorSubject<string>('');
  userRole$ = this.userRoleSubject.asObservable();

  private defaultUser: User = {
    fullName: '',
    birthday: '',
    email: '<EMAIL>',
    phoneNumber: '+216 00 000 000',
    governorate: 'Tunisie',
    gender: '',
    address: '',
    lastShoppingDate: 'Yesterday',
    role: '',
    avatar: 'assets/images/default-avatar.svg',
    showGenderPlaceholder: true,
    showAddressPlaceholder: true,
    showOccupationPlaceholder: true,
    showFullNamePlaceholder: true,
    hasCustomAvatar: false
  };

  // Initialize with default user, will be updated in constructor if localStorage has data
  private currentUserSubject = new BehaviorSubject<User>(this.defaultUser);
  currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // In development mode, clear localStorage on initialization
    this.clearLocalStorageForDevelopment();

    // Reset to default user
    this.currentUserSubject.next(this.defaultUser);
  }

  /**
   * Clear localStorage during development to start fresh on each reload
   */
  private clearLocalStorageForDevelopment(): void {
    // Clear any existing user data
    localStorage.removeItem('currentUser');

    // You can also clear other localStorage items if needed
    // localStorage.clear(); // Uncomment to clear all localStorage items

    console.log('Development mode: localStorage cleared on initialization');
  }

  setUserRole(role: string) {
    this.userRoleSubject.next(role);
  }

  getUserRole(): string {
    return this.userRoleSubject.value;
  }

  getCurrentUser(): User {
    return this.currentUserSubject.value;
  }

  updateUser(user: User): Observable<User> {
    // In development mode, we don't save to localStorage
    // localStorage.setItem('currentUser', JSON.stringify(user));

    // Just update the BehaviorSubject
    this.currentUserSubject.next(user);
    return of(user);
  }

  updateUserField(field: keyof User, value: any): Observable<User> {
    const currentUser = this.getCurrentUser();
    const updatedUser = { ...currentUser, [field]: value };
    return this.updateUser(updatedUser);
  }

  /**
   * Handle user login and update user email
   * @param emailOrUsername The email or username entered during login
   * @returns Observable of the updated user
   */
  handleLogin(emailOrUsername: string): Observable<User> {
    // Only update if it's an email (contains @)
    if (emailOrUsername.includes('@')) {
      const currentUser = this.getCurrentUser();
      const updatedUser = { ...currentUser, email: emailOrUsername };

      // In development mode, we'll still update the BehaviorSubject but won't persist to localStorage
      this.currentUserSubject.next(updatedUser);
      console.log('User logged in with email:', emailOrUsername);

      return of(updatedUser);
    }
    return of(this.getCurrentUser());
  }

  /**
   * Clear user data on logout
   */
  logout(): void {
    // In development mode, we don't need to remove from localStorage
    // localStorage.removeItem('currentUser');

    // Reset to default user
    this.currentUserSubject.next(this.defaultUser);
    console.log('User logged out');
  }
}