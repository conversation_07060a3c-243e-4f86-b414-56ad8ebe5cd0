/* Reset default styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  overflow: hidden;
  height: 100%;
}

/* Base styles for the Welcome to Receeto page */
.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: 'Poppins', Arial, sans-serif;
}

.welcome-header {
  text-align: center;
  margin-bottom: 50px;
}

.logo {
  max-width: 180px;
  margin-bottom: 20px;
}

h1 {
  font-size: 2.8rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 15px;
  letter-spacing: -0.5px;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #718096;
  max-width: 600px;
  text-align: center;
  line-height: 1.6;
}

.role-selection {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 1200px;
  width: 100%;
}

.role-container {
  flex: 1;
  min-width: 300px;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease;
}

.role-container:hover {
  transform: translateY(-10px);
}

.role-box {
  width: 100%;
  padding: 40px 30px;
  border-radius: 20px;
  background: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  text-align: center;
  position: relative;
  z-index: 1;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.role-box:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.role-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  z-index: 2;
}

.shopper-box::before {
  background: linear-gradient(90deg, #9f7aea, #b794f4);
}

.seller-box::before {
  background: linear-gradient(90deg, #9f7aea, #b794f4);
}

.role-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
}

.shopper-box .role-icon {
  background: rgba(159, 122, 234, 0.1);
  color: #9f7aea;
}

.seller-box .role-icon {
  background: rgba(159, 122, 234, 0.1);
  color: #9f7aea;
}

.role-box h2 {
  font-size: 1.8rem;
  margin-bottom: 15px;
  color: #2d3748;
  font-weight: 600;
}

.image-placeholder {
  width: 100%;
  height: 220px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: 20px 0;
  transition: transform 0.5s ease;
}

.role-box:hover .image-placeholder {
  transform: scale(1.05);
}

.shopper-box .image-placeholder {
  background-image: url('../../assets/images/shopper.png');
}

.seller-box .image-placeholder {
  background-image: url('../../assets/images/seller.png');
}

.select-btn {
  padding: 14px 48px;
  font-size: 1.1rem;
  border: none;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: -25px;
  position: relative;
  z-index: 3;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background: linear-gradient(90deg, #9f7aea, #b794f4);
  color: white;
}

.select-btn:hover {
  background: linear-gradient(90deg, #805ad5, #9f7aea);
  box-shadow: 0 7px 20px rgba(159, 122, 234, 0.3);
}

.footer {
  margin-top: 60px;
  text-align: center;
  color: #a0aec0;
  font-size: 0.9rem;
}

/* Tablet Responsive */
@media (max-width: 768px) {
  .welcome-container {
    padding: 30px 15px;
  }
  
  h1 {
    font-size: 2.2rem;
  }
  
  .welcome-subtitle {
    font-size: 1.1rem;
  }
  
  .role-selection {
    gap: 30px;
  }
  
  .role-container {
    min-width: 280px;
  }
  
  .role-box {
    padding: 30px 25px;
  }
  
  .role-box h2 {
    font-size: 1.6rem;
  }
  
  .image-placeholder {
    height: 180px;
  }
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .welcome-container {
    padding: 25px 15px;
  }
  
  h1 {
    font-size: 1.9rem;
  }
  
  .welcome-subtitle {
    font-size: 1rem;
  }
  
  .role-selection {
    gap: 40px;
  }
  
  .role-container {
    min-width: 100%;
  }
  
  .role-box {
    padding: 25px 20px;
  }
  
  .role-box h2 {
    font-size: 1.5rem;
  }
  
  .image-placeholder {
    height: 160px;
  }
  
  .select-btn {
    padding: 12px 40px;
    font-size: 1rem;
  }
}