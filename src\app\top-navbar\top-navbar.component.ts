import { Component, OnInit, After<PERSON>iew<PERSON>nit, HostL<PERSON>ener, OnD<PERSON>roy } from '@angular/core';
import { Subject, fromEvent } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { SidebarService } from '../sidebar/sidebar.service';
import { ThemeService } from '../services/theme.service';
import { RouterModule } from '@angular/router';
import { UserService } from '../services/user.service';
import { User } from '../models/user.model';
import { NotificationService } from '../notification-system/notification.service';
import { NotificationPanelComponent } from '../notification-system/notification-panel.component';

@Component({
  selector: 'app-top-navbar',
  templateUrl: './top-navbar.component.html',
  styleUrls: ['./top-navbar.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, NotificationPanelComponent]
})
export class TopNavbarComponent implements OnInit, AfterViewInit, OnDestroy {
  currentDate: Date = new Date();
  isDarkMode = false;
  isMobileMenuOpen = false;
  isMobile = window.innerWidth <= 768;
  private destroy$ = new Subject<void>();
  private mobileBreakpoint = 768;
  currentUser: User | null = null;

  constructor(
    private sidebarService: SidebarService,
    private themeService: ThemeService,
    private userService: UserService,
    private notificationService: NotificationService
  ) {
    this.themeService.isDarkMode$.subscribe(isDark => {
      this.isDarkMode = isDark;
    });

    // Subscribe to user changes
    this.userService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  // Remove HostListener and use RxJS for better performance

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const navbarRight = document.querySelector('.navbar-right');
    const avatarContainer = document.querySelector('.avatar-container');

    if (navbarRight && !navbarRight.contains(target) && !avatarContainer?.contains(target)) {
      this.closeMobileMenu();
    }
  }

  ngOnInit() {
    // Setup optimized resize listener with improved performance
    fromEvent(window, 'resize')
      .pipe(
        debounceTime(50), // Reduced debounce time for faster response
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        // Use requestAnimationFrame to ensure changes happen during the next paint cycle
        requestAnimationFrame(() => {
          const wasAlreadyMobile = this.isMobile;
          this.isMobile = window.innerWidth <= this.mobileBreakpoint;

          // Only trigger needed actions if the state actually changed
          if (wasAlreadyMobile !== this.isMobile) {
            // State changed, close mobile menu if we switched to desktop
            if (!this.isMobile) {
              this.isMobileMenuOpen = false;
            }
          }
        });
      });
  }

  ngAfterViewInit() {
    // No need to query DOM elements that aren't used
  }

  onSearchClick() {
    // Handle search click
  }

  onInfoClick() {
    // Handle info click
  }

  onNotificationsClick() {
    this.notificationService.toggleNotificationsPanel();
  }

  toggleTheme() {
    this.themeService.toggleTheme();
  }

  toggleSidebar() {
    if (this.isMobile) {
      this.sidebarService.toggleSidebar();
    }
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }



  ngOnDestroy() {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }
}