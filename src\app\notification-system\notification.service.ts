import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';

// Interface for notifications
export interface Notification {
  id: string;
  title: string;
  message: string;
  time: Date;
  read: boolean;
  detailContent?: string; // Optional detailed content to show when notification is selected
}

export interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

export interface NotificationSettings {
  brandOffers: NotificationSetting;
  marketingNotifications: NotificationSetting;
  newPartners: NotificationSetting;
  appUpdates: NotificationSetting;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private defaultSettings: NotificationSettings = {
    brandOffers: {
      id: 'brandOffers',
      title: 'Brand Offers Notifications',
      description: 'Offer Based On Your Shopping History',
      enabled: true
    },
    marketingNotifications: {
      id: 'marketingNotifications',
      title: 'Marketing Notifications',
      description: 'Receeto Notification About Our Offers',
      enabled: true
    },
    newPartners: {
      id: 'newPartners',
      title: 'New Partners Notifications',
      description: 'Get Informed About Every New Shopper',
      enabled: true
    },
    appUpdates: {
      id: 'appUpdates',
      title: 'App Updates',
      description: 'Get Informed About Our New Features',
      enabled: true
    }
  };

  private notificationSettingsSubject = new BehaviorSubject<NotificationSettings>(this.defaultSettings);
  notificationSettings$ = this.notificationSettingsSubject.asObservable();

  // Always start with notifications panel hidden
  private showNotificationsSubject = new BehaviorSubject<boolean>(false);
  showNotifications$ = this.showNotificationsSubject.asObservable();

  // Sample notifications - in a real app, these would come from a backend
  private notificationsSubject = new BehaviorSubject<Notification[]>([
    {
      id: '1',
      title: 'New Offer Available',
      message: 'Check out the latest offers from your favorite brands!',
      time: new Date(),
      read: false,
      detailContent: 'We have exciting new offers from brands you follow! Log in to your account to view personalized offers based on your shopping history. Limited time offers available until the end of the month.'
    },
    {
      id: '2',
      title: 'Receipt Processed',
      message: 'Your latest receipt has been processed successfully.',
      time: new Date(Date.now() - ********), // 1 day ago
      read: true,
      detailContent: 'Your receipt from Market Store has been processed. You earned 120 points from this purchase. Your current total is 1,450 points. Keep shopping to unlock premium rewards!'
    },
    {
      id: '3',
      title: 'Weekly Summary',
      message: 'Here\'s your shopping summary for the week.',
      time: new Date(Date.now() - *********), // 2 days ago
      read: true,
      detailContent: 'This week you saved $15.75 using our offers. Your most purchased categories were Groceries and Home Supplies. We recommend checking new offers in the Electronics category based on your recent searches.'
    }
  ]);
  notifications$ = this.notificationsSubject.asObservable();

  constructor(private router: Router) {
    // Clear notifications on application initialization
    // This ensures data is reset on page reload as per user preference
    this.clearNotifications();

    // Load saved settings if available
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
      this.notificationSettingsSubject.next(JSON.parse(savedSettings));
    }

    // We don't load saved notifications anymore to ensure they clear on reload
  }

  /**
   * Clear all notifications
   */
  private clearNotifications(): void {
    // Reset to empty array
    this.notificationsSubject.next([]);

    // Clear from localStorage
    localStorage.removeItem('notifications');
  }

  toggleNotificationSetting(settingId: string): void {
    const currentSettings = this.notificationSettingsSubject.value;
    const updatedSettings = { ...currentSettings };

    // Type assertion to access the property dynamically
    const setting = updatedSettings[settingId as keyof NotificationSettings];
    if (setting) {
      setting.enabled = !setting.enabled;

      // Save to localStorage
      this.notificationSettingsSubject.next(updatedSettings);
      localStorage.setItem('notificationSettings', JSON.stringify(updatedSettings));
    }
  }

  toggleNotificationsPanel(): void {
    // If we're on the user profile page, just emit true to trigger the flash animation
    if (this.router.url === '/user-profile') {
      this.showNotificationsSubject.next(true);
      return;
    }

    // Otherwise toggle as normal
    this.showNotificationsSubject.next(!this.showNotificationsSubject.value);
  }

  hideNotificationsPanel(): void {
    this.showNotificationsSubject.next(false);
  }

  getNotificationSettings(): Observable<NotificationSettings> {
    return this.notificationSettings$;
  }

  getShowNotifications(): Observable<boolean> {
    return this.showNotifications$;
  }

  /**
   * Get all notifications
   */
  getNotifications(): Observable<Notification[]> {
    return this.notifications$;
  }

  /**
   * Add a new notification
   * @param notification The notification to add
   */
  addNotification(notification: Omit<Notification, 'id'>): void {
    const currentNotifications = this.notificationsSubject.value;

    // Generate a unique ID
    const newId = Date.now().toString();

    // Create the new notification with ID
    const newNotification: Notification = {
      ...notification,
      id: newId
    };

    // Add to the beginning of the array
    const updatedNotifications = [newNotification, ...currentNotifications];

    // Update the subject
    this.notificationsSubject.next(updatedNotifications);

    // Save to localStorage
    this.saveNotificationsToLocalStorage(updatedNotifications);

    // Show the notifications panel
    this.showNotificationsSubject.next(true);
  }

  /**
   * Mark a notification as read
   * @param notificationId The ID of the notification to mark as read
   */
  markNotificationAsRead(notificationId: string): void {
    const currentNotifications = this.notificationsSubject.value;

    // Find and update the notification
    const updatedNotifications = currentNotifications.map(notification => {
      if (notification.id === notificationId) {
        return { ...notification, read: true };
      }
      return notification;
    });

    // Update the subject
    this.notificationsSubject.next(updatedNotifications);

    // Save to localStorage
    this.saveNotificationsToLocalStorage(updatedNotifications);
  }

  /**
   * Save notifications to localStorage
   * @param notifications The notifications to save
   * @param persistOnReload Whether to persist notifications after page reload (default: false)
   */
  private saveNotificationsToLocalStorage(notifications: Notification[], persistOnReload: boolean = false): void {
    // If persistOnReload is false, we don't save to localStorage
    // This ensures notifications are cleared on page reload
    if (!persistOnReload) {
      return;
    }

    try {
      localStorage.setItem('notifications', JSON.stringify(notifications));
    } catch (e) {
      console.error('Error saving notifications to localStorage:', e);
    }
  }

  /**
   * Add a purchase notification
   * @param brandName The name of the brand
   * @param productName The name of the product
   * @param price The price of the product
   * @param quantity The quantity purchased
   */
  addPurchaseNotification(brandName: string, productName: string, price: number, quantity: number = 1): void {
    const quantityText = quantity > 1 ? `${quantity} x ` : '';
    this.addNotification({
      title: 'Purchase Successful',
      message: `You have successfully purchased ${quantityText}${productName} from ${brandName}.`,
      time: new Date(),
      read: false,
      detailContent: `Thank you for your purchase of ${quantityText}${productName} from ${brandName} for ${price.toFixed(2)} TND. Your transaction has been recorded and can be viewed in your transaction history.`
    });
  }

  /**
   * Clear all notifications - public method that can be called from other components
   */
  clearAllNotifications(): void {
    this.clearNotifications();
  }
}
